import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/customer.dart';
import '../models/debt.dart';
import '../providers/debt_provider.dart';
import '../providers/card_type_provider.dart';
import '../widgets/debts_tab.dart';
import '../utils/number_formatter.dart';

class CustomerDebtsScreen extends StatefulWidget {
  const CustomerDebtsScreen({super.key, required this.customer});
  final Customer customer;

  @override
  State<CustomerDebtsScreen> createState() => _CustomerDebtsScreenState();
}

class _CustomerDebtsScreenState extends State<CustomerDebtsScreen>
    with TickerProviderStateMixin {
  bool _isStatisticsExpanded = false;
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();

    // إعداد الأنيميشن
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    // Load customer debts
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final debtProvider = Provider.of<DebtProvider>(context, listen: false);
      debtProvider.loadCustomerDebts(widget.customer.id!);
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Refresh debts when returning to this screen
    final debtProvider = Provider.of<DebtProvider>(context, listen: false);
    if (debtProvider.currentCustomerId == widget.customer.id) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        debtProvider.refreshCurrentCustomerDebts();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('قائمة الديون'),
        backgroundColor: const Color(0xFF00695C),
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        actions: [
          // زر الإحصائيات
          IconButton(
            onPressed: _toggleStatistics,
            icon: AnimatedRotation(
              turns: _isStatisticsExpanded ? 0.5 : 0,
              duration: const Duration(milliseconds: 300),
              child: Icon(
                _isStatisticsExpanded
                    ? Icons.expand_less
                    : Icons.analytics_outlined,
              ),
            ),
            tooltip:
                _isStatisticsExpanded ? 'إخفاء الإحصائيات' : 'إظهار الإحصائيات',
          ),
        ],
      ),
      body: Column(
        children: [
          // قسم الإحصائيات القابل للطي
          SizeTransition(
            sizeFactor: _animation,
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                border: Border(
                  bottom: BorderSide(
                    color: Colors.grey.shade300,
                  ),
                ),
              ),
              child: _buildStatisticsContent(),
            ),
          ),

          // قائمة الديون
          Expanded(
            child: DebtsTab(customer: widget.customer),
          ),
        ],
      ),
    );
  }

  // تبديل حالة الإحصائيات
  void _toggleStatistics() {
    setState(() {
      _isStatisticsExpanded = !_isStatisticsExpanded;
      if (_isStatisticsExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  // بناء محتوى الإحصائيات
  Widget _buildStatisticsContent() {
    return Consumer<DebtProvider>(
      builder: (context, debtProvider, child) {
        // فلترة الديون النشطة فقط (نفس الفلترة المستخدمة في قائمة الديون)
        final customerDebts = debtProvider.debts
            .where((debt) =>
                debt.customerId == widget.customer.id &&
                debt.status != DebtStatus.paid)
            .toList();

        // حساب الإحصائيات
        final stats = _calculateStatistics(customerDebts);

        return Container(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height *
                0.6, // حد أقصى 60% من ارتفاع الشاشة
          ),
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            physics: const BouncingScrollPhysics(), // تأثير مرن للتمرير
            child: Column(
              children: [
                // بطاقة إجمالي المبلغ العريضة
                _buildTotalAmountCard(stats),
                const SizedBox(height: 16),

                // بطاقة إجمالي الكروت
                _buildTotalCardsCard(stats),
                const SizedBox(height: 16),

                // إحصائيات كل نوع كارت
                if (stats['cardTypes'].isNotEmpty) ...[
                  _buildCardTypesHeader(),
                  const SizedBox(height: 12),
                  ...stats['cardTypes'].entries.map(
                        (entry) => _buildCardTypeStatCard(
                          _convertCardTypeToArabic(entry.key),
                          entry.value,
                        ),
                      ),
                ],

                // مساحة إضافية في النهاية للتمرير المريح
                const SizedBox(height: 20),
              ],
            ),
          ),
        );
      },
    );
  }

  // حساب الإحصائيات

  Map<String, dynamic> _calculateStatistics(List<Debt> debts) {
    double totalAmount = 0;
    int totalCards = 0;
    final Map<String, Map<String, dynamic>> cardTypes = {};

    for (final debt in debts) {
      totalAmount += debt.remainingAmount; // المبلغ المتبقي للديون النشطة
      totalCards += debt.quantity;

      if (cardTypes.containsKey(debt.cardType)) {
        cardTypes[debt.cardType]!['amount'] +=
            debt.remainingAmount; // المبلغ المتبقي للديون النشطة
        cardTypes[debt.cardType]!['count'] += debt.quantity;
      } else {
        cardTypes[debt.cardType] = {
          'amount': debt.remainingAmount, // المبلغ المتبقي للديون النشطة
          'count': debt.quantity,
        };
      }
    }

    return {
      'totalAmount': totalAmount,
      'totalCards': totalCards,
      'cardTypes': cardTypes,
    };
  }

  // بناء بطاقة إجمالي المبلغ العريضة
  Widget _buildTotalAmountCard(Map<String, dynamic> stats) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // العنوان
          Text(
            'إجمالي المبلغ',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 16),

          // الأيقونة والمبلغ
          Row(
            children: [
              // أيقونة المبلغ
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.shade100,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.green.shade300),
                ),
                child: Icon(
                  Icons.attach_money,
                  color: Colors.green.shade700,
                  size: 24,
                ),
              ),
              const SizedBox(width: 20),

              // المبلغ ووحدة العملة
              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // المبلغ
                    Text(
                      NumberFormatter.formatCurrencyWithoutSymbol(
                        stats['totalAmount'],
                      ),
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey.shade800,
                      ),
                    ),
                    const SizedBox(width: 12),

                    // وحدة العملة بنفس حجم الرقم
                    Text(
                      'ألف',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey.shade800,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // بناء بطاقة إجمالي الكروت
  Widget _buildTotalCardsCard(Map<String, dynamic> stats) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          // أيقونة الكروت
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.shade100,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue.shade300),
            ),
            child: Icon(
              Icons.credit_card,
              color: Colors.blue.shade700,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),

          // معلومات الكروت
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إجمالي الكروت',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey.shade700,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  NumberFormatter.formatNumber(stats['totalCards']),
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade800,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // بناء عنوان أنواع الكروت
  Widget _buildCardTypesHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        children: [
          Icon(Icons.category_outlined, color: Colors.grey.shade700, size: 20),
          const SizedBox(width: 12),
          Text(
            'تفاصيل أنواع الكروت',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
        ],
      ),
    );
  }

  // بناء بطاقة إحصائيات نوع كارت
  Widget _buildCardTypeStatCard(String cardType, Map<String, dynamic> data) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // أيقونة نوع الكارت
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: Colors.orange.shade100,
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: Colors.orange.shade300),
            ),
            child: Icon(
              Icons.credit_card,
              color: Colors.orange.shade700,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),

          // معلومات نوع الكارت
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  cardType,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade800,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'العدد',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade600,
                            ),
                          ),
                          Text(
                            NumberFormatter.formatNumber(data['count']),
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey.shade800,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'المبلغ',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade600,
                            ),
                          ),
                          Text(
                            NumberFormatter.formatCurrencyWithoutSymbol(
                              data['amount'],
                            ),
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey.shade800,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // الحصول على اسم الكارت الصحيح من قاعدة البيانات أو الترجمة
  String _convertCardTypeToArabic(String cardType) {
    if (cardType.isEmpty) return 'غير محدد';

    // إذا كان النص عربي بالفعل، أعده كما هو
    if (_isArabicText(cardType)) {
      return cardType.trim();
    }

    // محاولة الحصول على الاسم من CardTypeProvider أولاً
    try {
      final cardTypeProvider = Provider.of<CardTypeProvider>(
        context,
        listen: false,
      );

      // البحث بالمعرف المباشر
      final cardTypeOption = cardTypeProvider.getCardTypeById(cardType);
      if (cardTypeOption != null) {
        return cardTypeOption.displayName;
      }

      // البحث في الأنواع المخصصة
      final customCardType = cardTypeProvider.customCardTypes
          .where((ct) => ct.name == cardType || ct.displayName == cardType)
          .firstOrNull;
      if (customCardType != null) {
        return customCardType.displayName;
      }
    } catch (e) {
      debugPrint('Error getting card type from provider: $e');
    }

    // إذا لم نجد في Provider، نحاول الترجمة اليدوية
    return _fallbackTranslateCardType(cardType);
  }

  // ترجمة احتياطية للأنواع المعروفة
  String _fallbackTranslateCardType(String cardType) {
    // تنظيف النص مع الاحتفاظ بالأرقام للأنواع المخصصة
    final originalLower = cardType.trim().toLowerCase();

    // إذا كان النوع مخصص بأرقام (مثل Custom_3, Custom_5)
    if (originalLower.startsWith('custom_') &&
        RegExp(r'custom_\d+$').hasMatch(originalLower)) {
      final number = originalLower.replaceAll('custom_', '');
      return 'نوع مخصص $number';
    }

    final cleanType = cardType
        .trim()
        .toLowerCase()
        .replaceAll(
          RegExp(r'[^a-zA-Z\u0600-\u06FF]'),
          '',
        ) // إزالة كل شيء عدا الحروف
        .replaceAll('custom', '') // إزالة كلمة custom
        .trim();

    // البحث الذكي بالكلمات المفتاحية - حل جذري وشامل

    // زين - جميع الأشكال الممكنة
    if (_containsAny(cleanType, [
      'zain',
      'زين',
      'zaincard',
      'zaincash',
      'zainmoney',
      'zainiraq',
      'zaintelecom',
      'zaintel',
      'zn',
      'zain5000',
      'zain10000',
    ])) {
      return 'زين';
    }

    // آسيا - جميع الأشكال الممكنة
    if (_containsAny(cleanType, [
      'asia',
      'sia',
      'آسيا',
      'اسيا',
      'asiacard',
      'siacard',
      'asiacash',
      'siacash',
      'asiacell',
      'siacell',
      'asiatelecom',
      'siatelecom',
      'asiacel',
      'siacel',
      'asia5000',
      'sia5000',
      'asia10000',
      'sia10000',
    ])) {
      return 'آسيا';
    }

    // أبو العشرة - جميع الأشكال الممكنة (البحث الأكثر تحديداً أولاً)
    if (_containsAny(cleanType, [
      'abuashara',
      'ashara',
      'abuashara',
      'abuasharacard',
      'asharacard',
      'abuasharacash',
      'asharacash',
      'أبوالعشرة',
      'ابوالعشرة',
      'أبوعشرة',
      'ابوعشرة',
      'العشرة',
      'عشرة',
    ])) {
      return 'أبو العشرة';
    }

    // أبو الستة - جميع الأشكال الممكنة (البحث الأكثر تحديداً أولاً)
    if (_containsAny(cleanType, [
      'abusitta',
      'sitta',
      'abusitta',
      'abusittacard',
      'sittacard',
      'abusittacash',
      'sittacash',
      'أبوالستة',
      'ابوالستة',
      'أبوستة',
      'ابوستة',
      'الستة',
      'ستة',
    ])) {
      return 'أبو الستة';
    }

    // الأنواع الأساسية
    if (_containsAny(cleanType, ['cash', 'نقدي'])) {
      return 'نقدي';
    }

    if (_containsAny(cleanType, ['visa', 'فيزا'])) {
      return 'فيزا';
    }

    if (_containsAny(cleanType, ['mastercard', 'master', 'ماستر'])) {
      return 'ماستركارد';
    }

    if (_containsAny(cleanType, [
      'americanexpress',
      'american',
      'express',
      'امريكان',
    ])) {
      return 'أمريكان إكسبريس';
    }

    if (_containsAny(cleanType, ['other', 'اخرى', 'أخرى'])) {
      return 'أخرى';
    }

    // إذا لم نجد مطابقة، نعيد الاسم الأصلي مع تحسين التنسيق
    return _capitalizeFirst(cardType.trim());
  }

  // دالة مساعدة للبحث في قائمة من الكلمات المفتاحية - محسنة
  bool _containsAny(String text, List<String> keywords) {
    final lowerText = text.toLowerCase();
    for (final keyword in keywords) {
      final lowerKeyword = keyword.toLowerCase();
      // بحث دقيق ومرن
      if (lowerText.contains(lowerKeyword) ||
          lowerText == lowerKeyword ||
          lowerText.startsWith(lowerKeyword) ||
          lowerText.endsWith(lowerKeyword)) {
        return true;
      }
    }
    return false;
  }

  // تحسين تنسيق النص
  String _capitalizeFirst(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1).toLowerCase();
  }

  // فحص إذا كان النص عربي
  bool _isArabicText(String text) {
    if (text.isEmpty) return false;
    return RegExp(r'[\u0600-\u06FF]').hasMatch(text);
  }
}
