import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:io';

// تم حذف الاستيرادات غير المستخدمة مؤقتاً
import '../services/local_notification_service.dart';
import '../services/notification_scheduler_service.dart';
import '../models/notification_schedule.dart';
import '../services/backup_service.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _isDarkMode = false;
  bool _notificationsEnabled = true;
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;
  bool _autoBackup = false;

  String _appVersion = '';
  final NotificationSchedulerService _schedulerService =
      NotificationSchedulerService();
  final List<NotificationSchedule> _schedules = [];

  @override
  void initState() {
    super.initState();
    _loadSettings();
    _loadAppVersion();
    _initializeScheduler();
  }

  // تهيئة خدمة الجدولة
  Future<void> _initializeScheduler() async {
    try {
      await _schedulerService.initialize();
      setState(() {
        _schedules.clear();
        _schedules.addAll(_schedulerService.schedules);
      });
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة الجدولة: $e');
    }
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _isDarkMode = prefs.getBool('dark_mode') ?? false;
      _notificationsEnabled = prefs.getBool('notifications') ?? true;
      _soundEnabled = prefs.getBool('sound') ?? true;
      _vibrationEnabled = prefs.getBool('vibration') ?? true;
      _autoBackup = prefs.getBool('auto_backup') ?? false;
    });
  }

  Future<void> _loadAppVersion() async {
    final packageInfo = await PackageInfo.fromPlatform();
    setState(() {
      _appVersion = '${packageInfo.version} (${packageInfo.buildNumber})';
    });
  }

  Future<void> _saveSetting(String key, dynamic value) async {
    final prefs = await SharedPreferences.getInstance();
    if (value is bool) {
      await prefs.setBool(key, value);
    } else if (value is String) {
      await prefs.setString(key, value);
    }
  }

  // نظام تنبيهات بسيط - إرسال تنبيهات فورية
  Future<void> _sendInstantNotification(String type) async {
    try {
      await LocalNotificationService().initialize();

      String title = '';

      switch (type) {
        case 'overdue':
          title = '🔴 تنبيه ديون متأخرة';
          break;
        case 'due_today':
          title = '🟠 تنبيه ديون مستحقة اليوم';
          break;
        case 'summary':
          title = '📊 ملخص الديون';
          break;
        default:
          title = '🔔 تنبيه عام';
      }

      await LocalNotificationService().testNotification();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم إرسال $title'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ خطأ في إرسال التنبيه: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل في إرسال التنبيه'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  // عرض خيارات التنبيهات البسيطة
  void _showSimpleNotificationOptions() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تنبيهات بسيطة'),
        content: Column(
          children: [
            ListTile(
              leading: const Icon(Icons.warning, color: Colors.red),
              title: const Text('ديون متأخرة'),
              onTap: () {
                Navigator.pop(context);
                _sendInstantNotification('overdue');
              },
            ),
            ListTile(
              leading: const Icon(Icons.schedule, color: Colors.orange),
              title: const Text('ديون مستحقة اليوم'),
              onTap: () {
                Navigator.pop(context);
                _sendInstantNotification('due_today');
              },
            ),
            ListTile(
              leading: const Icon(Icons.summarize, color: Colors.blue),
              title: const Text('ملخص الديون'),
              onTap: () {
                Navigator.pop(context);
                _sendInstantNotification('summary');
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  // عرض حوار جدولة التنبيهات
  void _showScheduledNotificationsDialog() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.7,
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              // العنوان
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade100,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.schedule,
                      color: Colors.blue.shade600,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'جدولة التنبيهات',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2C3E50),
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // قائمة التنبيهات
              Expanded(
                child: _schedules.isEmpty
                    ? const Center(
                        child: Text(
                          'لا توجد تنبيهات مجدولة',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey,
                          ),
                        ),
                      )
                    : ListView.builder(
                        itemCount: _schedules.length,
                        itemBuilder: (context, index) {
                          final schedule = _schedules[index];
                          return _buildScheduleCard(schedule);
                        },
                      ),
              ),

              const SizedBox(height: 16),

              // أزرار العمل
              Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () => _showAddScheduleDialog(),
                          icon: const Icon(Icons.add),
                          label: const Text('إضافة تنبيه'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue.shade600,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed:
                              _schedules.where((s) => s.isEnabled).isEmpty
                                  ? null
                                  : () => _testScheduledNotifications(),
                          icon: const Icon(Icons.bug_report),
                          label: const Text('اختبار التنبيهات المفعلة'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Colors.orange.shade600,
                            side: BorderSide(color: Colors.orange.shade300),
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // بناء كارت الجدولة - إعادة كتابة كاملة
  Widget _buildScheduleCard(NotificationSchedule schedule) {
    return StatefulBuilder(
      builder: (context, setCardState) {
        // البحث عن الجدولة الحالية
        final currentIndex = _schedules.indexWhere((s) => s.id == schedule.id);
        final currentSchedule =
            currentIndex >= 0 ? _schedules[currentIndex] : schedule;

        return Container(
          key: ValueKey('schedule_card_${currentSchedule.id}'),
          margin: const EdgeInsets.only(bottom: 12),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color:
                currentSchedule.isEnabled ? Colors.white : Colors.grey.shade50,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: currentSchedule.isEnabled
                  ? Colors.blue.shade200
                  : Colors.grey.shade300,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Icon(
                    _getScheduleIcon(currentSchedule.type),
                    color: currentSchedule.isEnabled
                        ? Colors.blue.shade600
                        : Colors.grey,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      currentSchedule.name,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: currentSchedule.isEnabled
                            ? Colors.black87
                            : Colors.grey,
                      ),
                    ),
                  ),
                  Switch(
                    value: currentSchedule.isEnabled,
                    onChanged: (newValue) {
                      debugPrint(
                          '🔄 تبديل التنبيه ${currentSchedule.id}: $newValue');

                      // تحديث فوري للكارت
                      setCardState(() {
                        // تحديث في القائمة الرئيسية
                        if (currentIndex >= 0) {
                          _schedules[currentIndex] = _schedules[currentIndex]
                              .copyWith(isEnabled: newValue);
                        }
                      });

                      // تحديث الواجهة الرئيسية
                      setState(() {});

                      // تحديث الخدمة
                      _schedulerService
                          .toggleSchedule(currentSchedule.id, newValue)
                          .catchError((error) {
                        debugPrint('❌ خطأ في التحديث: $error');

                        // إعادة الحالة السابقة
                        setCardState(() {
                          if (currentIndex >= 0) {
                            _schedules[currentIndex] = _schedules[currentIndex]
                                .copyWith(isEnabled: !newValue);
                          }
                        });
                        setState(() {});

                        if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('فشل في تحديث التنبيه: $error'),
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      });
                    },
                    activeColor: Colors.blue.shade600,
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 16,
                    color: Colors.grey.shade600,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    schedule.time.format(context),
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Icon(
                    Icons.repeat,
                    size: 16,
                    color: Colors.grey.shade600,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _getWeekdaysText(schedule.weekdays),
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                schedule.type.displayName,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade500,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton.icon(
                    onPressed: () => _showEditScheduleDialog(schedule),
                    icon: const Icon(Icons.edit, size: 16),
                    label: const Text('تعديل'),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.blue.shade600,
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                    ),
                  ),
                  TextButton.icon(
                    onPressed: () => _deleteSchedule(schedule),
                    icon: const Icon(Icons.delete, size: 16),
                    label: const Text('حذف'),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.red.shade600,
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  // الحصول على أيقونة نوع الجدولة
  IconData _getScheduleIcon(NotificationScheduleType type) {
    switch (type) {
      case NotificationScheduleType.summary:
        return Icons.summarize;
      case NotificationScheduleType.overdue:
        return Icons.warning;
      case NotificationScheduleType.dueToday:
        return Icons.today;
      case NotificationScheduleType.dueSoon:
        return Icons.schedule;
      case NotificationScheduleType.custom:
        return Icons.notifications;
      default:
        return Icons.alarm;
    }
  }

  // تحويل أيام الأسبوع إلى نص
  String _getWeekdaysText(List<int> weekdays) {
    if (weekdays.length == 7) return 'يومياً';
    if (weekdays.length == 5 &&
        !weekdays.contains(6) &&
        !weekdays.contains(7)) {
      return 'أيام العمل';
    }
    if (weekdays.length == 2 && weekdays.contains(6) && weekdays.contains(7)) {
      return 'نهاية الأسبوع';
    }

    final dayNames = [
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد'
    ];
    return weekdays.map((day) => dayNames[day - 1]).join(', ');
  }

  // تحديث قائمة الجدولة
  Future<void> _refreshSchedules() async {
    setState(() {
      _schedules.clear();
      _schedules.addAll(_schedulerService.schedules);
    });
  }

  // اختبار التنبيهات المجدولة
  Future<void> _testScheduledNotifications() async {
    try {
      await _schedulerService.testScheduledNotifications();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ تم إرسال تنبيهات الاختبار بنجاح'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ خطأ في اختبار التنبيهات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // حذف جدولة
  Future<void> _deleteSchedule(NotificationSchedule schedule) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل تريد حذف "${schedule.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _schedulerService.deleteSchedule(schedule.id);
      await _refreshSchedules();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم حذف "${schedule.name}"'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  // عرض حوار إضافة جدولة جديدة
  void _showAddScheduleDialog() {
    _showScheduleFormDialog(null);
  }

  // عرض حوار تعديل جدولة
  void _showEditScheduleDialog(NotificationSchedule schedule) {
    _showScheduleFormDialog(schedule);
  }

  // عرض نموذج الجدولة (إضافة أو تعديل)
  void _showScheduleFormDialog(NotificationSchedule? existingSchedule) {
    // إنشاء controllers للحقول
    final nameController =
        TextEditingController(text: existingSchedule?.name ?? '');
    final customMessageController =
        TextEditingController(text: existingSchedule?.customMessage ?? '');

    TimeOfDay selectedTime =
        existingSchedule?.time ?? const TimeOfDay(hour: 9, minute: 0);
    NotificationScheduleType selectedType =
        existingSchedule?.type ?? NotificationScheduleType.summary;
    final List<int> selectedWeekdays =
        List.from(existingSchedule?.weekdays ?? [1, 2, 3, 4, 5, 6, 7]);

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Text(
            existingSchedule == null ? 'إضافة تنبيه جديد' : 'تعديل التنبيه',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2C3E50),
            ),
          ),
          contentPadding: const EdgeInsets.fromLTRB(24, 20, 24, 0),
          content: ConstrainedBox(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.7,
              maxWidth: MediaQuery.of(context).size.width * 0.9,
            ),
            child: IntrinsicHeight(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // اسم التنبيه
                    Padding(
                      padding: const EdgeInsets.only(bottom: 16),
                      child: TextField(
                        controller: nameController,
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.black87,
                        ),
                        decoration: InputDecoration(
                          labelText: 'اسم التنبيه',
                          labelStyle: const TextStyle(
                            color: Colors.black54,
                            fontSize: 14,
                          ),
                          hintText: 'أدخل اسم التنبيه',
                          hintStyle: const TextStyle(
                            color: Colors.grey,
                            fontSize: 14,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(color: Colors.grey.shade300),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(color: Colors.grey.shade300),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide:
                                const BorderSide(color: Colors.blue, width: 2),
                          ),
                          prefixIcon: const Icon(Icons.label_outline,
                              color: Colors.blue),
                          filled: true,
                          fillColor: Colors.white,
                          contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 16),
                        ),
                      ),
                    ),

                    // نوع التنبيه
                    Padding(
                      padding: const EdgeInsets.only(bottom: 16),
                      child: DropdownButtonFormField<NotificationScheduleType>(
                        value: selectedType,
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.black87,
                        ),
                        decoration: InputDecoration(
                          labelText: 'نوع التنبيه',
                          labelStyle: const TextStyle(
                            color: Colors.black54,
                            fontSize: 14,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(color: Colors.grey.shade300),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(color: Colors.grey.shade300),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide:
                                const BorderSide(color: Colors.blue, width: 2),
                          ),
                          prefixIcon: const Icon(Icons.category_outlined,
                              color: Colors.blue),
                          filled: true,
                          fillColor: Colors.white,
                          contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 16),
                        ),
                        items: NotificationScheduleType.values.map((type) {
                          return DropdownMenuItem(
                            value: type,
                            child: Text(
                              type.displayName,
                              style: const TextStyle(
                                fontSize: 16,
                                color: Colors.black87,
                              ),
                            ),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setDialogState(() {
                              selectedType = value;
                            });
                          }
                        },
                      ),
                    ),

                    // الوقت
                    Padding(
                      padding: const EdgeInsets.only(bottom: 16),
                      child: Material(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        child: InkWell(
                          borderRadius: BorderRadius.circular(12),
                          onTap: () async {
                            final time = await showTimePicker(
                              context: context,
                              initialTime: selectedTime,
                            );
                            if (time != null) {
                              setDialogState(() {
                                selectedTime = time;
                              });
                            }
                          },
                          child: Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey.shade300),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              children: [
                                const Icon(Icons.access_time,
                                    color: Colors.blue),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const Text(
                                        'الوقت',
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Colors.black54,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        selectedTime.format(context),
                                        style: const TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w500,
                                          color: Colors.black87,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const Icon(Icons.edit, color: Colors.grey),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),

                    // أيام الأسبوع
                    Padding(
                      padding: const EdgeInsets.only(bottom: 16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'أيام التكرار:',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Color(0xFF2C3E50),
                            ),
                          ),
                          const SizedBox(height: 12),
                          Wrap(
                            spacing: 8,
                            runSpacing: 8,
                            children: [
                              for (int i = 1; i <= 7; i++)
                                FilterChip(
                                  label: Text(
                                    _getDayName(i),
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: selectedWeekdays.contains(i)
                                          ? Colors.blue.shade700
                                          : Colors.black87,
                                    ),
                                  ),
                                  selected: selectedWeekdays.contains(i),
                                  onSelected: (selected) {
                                    setDialogState(() {
                                      if (selected) {
                                        selectedWeekdays.add(i);
                                      } else {
                                        selectedWeekdays.remove(i);
                                      }
                                    });
                                  },
                                  selectedColor: Colors.blue.shade100,
                                  checkmarkColor: Colors.blue.shade700,
                                  backgroundColor: Colors.grey.shade100,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    side: BorderSide(
                                      color: selectedWeekdays.contains(i)
                                          ? Colors.blue.shade300
                                          : Colors.grey.shade300,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    // رسالة مخصصة (للتنبيهات المخصصة)
                    if (selectedType == NotificationScheduleType.custom) ...[
                      Padding(
                        padding: const EdgeInsets.only(bottom: 16),
                        child: TextField(
                          controller: customMessageController,
                          style: const TextStyle(
                            fontSize: 16,
                            color: Colors.black87,
                          ),
                          maxLines: 3,
                          decoration: InputDecoration(
                            labelText: 'رسالة مخصصة',
                            labelStyle: const TextStyle(
                              color: Colors.black54,
                              fontSize: 14,
                            ),
                            hintText: 'أدخل رسالة التنبيه المخصصة',
                            hintStyle: const TextStyle(
                              color: Colors.grey,
                              fontSize: 14,
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide:
                                  BorderSide(color: Colors.grey.shade300),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide:
                                  BorderSide(color: Colors.grey.shade300),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: const BorderSide(
                                  color: Colors.blue, width: 2),
                            ),
                            prefixIcon: const Icon(Icons.message_outlined,
                                color: Colors.blue),
                            filled: true,
                            fillColor: Colors.white,
                            contentPadding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 16),
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                nameController.dispose();
                customMessageController.dispose();
                Navigator.pop(context);
              },
              style: TextButton.styleFrom(
                foregroundColor: Colors.grey.shade600,
                padding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              ),
              child: const Text(
                'إلغاء',
                style: TextStyle(fontSize: 16),
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: () async {
                if (nameController.text.trim().isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('يرجى إدخال اسم التنبيه'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                if (selectedWeekdays.isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('يرجى اختيار يوم واحد على الأقل'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                final schedule = NotificationSchedule(
                  id: existingSchedule?.id ?? 0,
                  name: nameController.text.trim(),
                  time: selectedTime,
                  weekdays: selectedWeekdays,
                  isEnabled: existingSchedule?.isEnabled ?? true,
                  type: selectedType,
                  customMessage: selectedType == NotificationScheduleType.custom
                      ? customMessageController.text.trim()
                      : null,
                );

                try {
                  if (existingSchedule == null) {
                    await _schedulerService.addSchedule(schedule);
                  } else {
                    await _schedulerService.updateSchedule(schedule);
                  }

                  await _refreshSchedules();

                  // تنظيف الـ controllers
                  nameController.dispose();
                  customMessageController.dispose();

                  if (mounted) {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(existingSchedule == null
                            ? 'تم إضافة التنبيه بنجاح'
                            : 'تم تحديث التنبيه بنجاح'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  }
                } catch (e) {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('خطأ: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue.shade600,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(existingSchedule == null ? 'إضافة' : 'تحديث'),
            ),
          ],
        ),
      ),
    );
  }

  // الحصول على اسم اليوم
  String _getDayName(int day) {
    const dayNames = [
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد'
    ];
    return dayNames[day - 1];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('الإعدادات'),
        backgroundColor: const Color(0xFF2A5298), // لون أزرق مميز
        foregroundColor: Colors.white,
        elevation: 8,
        centerTitle: false,
        shadowColor: const Color(0xFF2A5298).withValues(alpha: 0.3),
        titleTextStyle: const TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.w500,
          color: Colors.white,
        ),
        iconTheme: const IconThemeData(
          color: Colors.white,
          size: 24,
        ),
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(20),
          ),
        ),
      ),
      body: ListView(
        children: [
          const SizedBox(height: 16),

          // قسم المظهر
          buildSectionHeader('المظهر'),
          buildSwitchTile(
            icon: Icons.dark_mode_outlined,
            title: 'الوضع الليلي',
            subtitle: 'تفعيل المظهر الداكن',
            value: _isDarkMode,
            onChanged: (value) {
              setState(() {
                _isDarkMode = value;
              });
              _saveSetting('dark_mode', value);
            },
          ),

          buildSettingsTile(
            icon: Icons.text_fields_outlined,
            title: 'إعدادات الخط',
            subtitle: 'تخصيص حجم ونوع الخط',
            onTap: () {
              // سيتم تنفيذ إعدادات الخط لاحقاً
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('إعدادات الخط قيد التطوير'),
                  backgroundColor: Colors.orange,
                ),
              );
            },
          ),

          const SizedBox(height: 16),

          // قسم الإشعارات
          buildSectionHeader('الإشعارات'),
          buildSwitchTile(
            icon: Icons.notifications_outlined,
            title: 'الإشعارات',
            subtitle: 'تلقي التنبيهات والإشعارات',
            value: _notificationsEnabled,
            onChanged: (value) {
              setState(() {
                _notificationsEnabled = value;
              });
              _saveSetting('notifications', value);
            },
          ),
          buildSettingsTile(
            icon: Icons.notifications_active_outlined,
            title: 'تنبيهات بسيطة',
            subtitle: 'إرسال تنبيهات فورية للديون المهمة',
            onTap: () {
              _showSimpleNotificationOptions();
            },
          ),
          buildSettingsTile(
            icon: Icons.schedule_outlined,
            title: 'جدولة التنبيهات',
            subtitle:
                'إدارة التنبيهات المجدولة (${_schedules.where((s) => s.isEnabled).length} مفعل)',
            onTap: () {
              _showScheduledNotificationsDialog();
            },
          ),
          buildSwitchTile(
            icon: Icons.volume_up_outlined,
            title: 'الصوت',
            subtitle: 'تشغيل أصوات الإشعارات',
            value: _soundEnabled,
            onChanged: (value) {
              setState(() {
                _soundEnabled = value;
              });
              _saveSetting('sound', value);
            },
          ),
          buildSwitchTile(
            icon: Icons.vibration_outlined,
            title: 'الاهتزاز',
            subtitle: 'تفعيل الاهتزاز للإشعارات',
            value: _vibrationEnabled,
            onChanged: (value) {
              setState(() {
                _vibrationEnabled = value;
              });
              _saveSetting('vibration', value);
            },
          ),

          const SizedBox(height: 16),

          // قسم البيانات
          buildSectionHeader('البيانات والنسخ الاحتياطي'),
          buildSwitchTile(
            icon: Icons.backup_outlined,
            title: 'النسخ الاحتياطي التلقائي',
            subtitle: 'حفظ البيانات تلقائياً',
            value: _autoBackup,
            onChanged: (value) {
              setState(() {
                _autoBackup = value;
              });
              _saveSetting('auto_backup', value);
            },
          ),
          buildSettingsTile(
            icon: Icons.cloud_upload_outlined,
            title: 'تصدير البيانات',
            subtitle: 'حفظ نسخة من البيانات',
            onTap: () {
              _showExportDialog();
            },
          ),
          buildSettingsTile(
            icon: Icons.cloud_download_outlined,
            title: 'استيراد البيانات',
            subtitle: 'استعادة البيانات من نسخة احتياطية',
            onTap: () {
              _showImportDialog();
            },
          ),

          const SizedBox(height: 16),

          const SizedBox(height: 16),

          // قسم حول التطبيق
          buildSectionHeader('حول التطبيق'),
          buildSettingsTile(
            icon: Icons.info_outline,
            title: 'معلومات التطبيق',
            subtitle: 'الإصدار $_appVersion',
            onTap: () {
              _showAboutDialog();
            },
          ),

          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: Colors.blue[700],
          letterSpacing: 0.5,
        ),
      ),
    );
  }

  // دالة لتحديد لون الأيقونة حسب النوع
  Color getIconColor(IconData icon) {
    switch (icon) {
      case Icons.person_outline:
        return const Color(0xFF2196F3); // أزرق
      case Icons.security_outlined:
        return const Color(0xFF4CAF50); // أخضر
      case Icons.dark_mode_outlined:
        return const Color(0xFF9C27B0); // بنفسجي
      case Icons.text_fields_outlined:
        return const Color(0xFF607D8B); // رمادي مزرق
      case Icons.notifications_outlined:
      case Icons.notifications_active_outlined:
        return const Color(0xFFF44336); // أحمر
      case Icons.volume_up_outlined:
      case Icons.vibration_outlined:
        return const Color(0xFF3F51B5); // نيلي
      case Icons.bug_report_outlined:
        return const Color(0xFFFF5722); // برتقالي محمر
      case Icons.backup_outlined:
      case Icons.cloud_upload_outlined:
        return const Color(0xFF00BCD4); // سماوي
      case Icons.cloud_download_outlined:
        return const Color(0xFF9C27B0); // بنفسجي
      case Icons.help_outline:
        return const Color(0xFF009688); // تيل
      case Icons.feedback_outlined:
        return const Color(0xFF795548); // بني
      case Icons.star_outline:
        return const Color(0xFFFFC107); // ذهبي
      case Icons.info_outline:
        return const Color(0xFF2A5298); // أزرق داكن
      case Icons.description_outlined:
      case Icons.privacy_tip_outlined:
        return const Color(0xFF607D8B); // رمادي
      default:
        return const Color(0xFF2A5298); // اللون الافتراضي
    }
  }

  Widget buildSettingsTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    final Color iconColor = getIconColor(icon);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 3),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.08),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: iconColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: iconColor.withValues(alpha: 0.3),
              width: 1.5,
            ),
          ),
          child: Icon(icon, color: iconColor, size: 22),
        ),
        title: Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            fontSize: 13,
            color: Colors.grey[600],
            height: 1.2,
          ),
        ),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: Color(0xFF2A5298),
        ),
        onTap: onTap,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      ),
    );
  }

  Widget buildSwitchTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    final Color iconColor = getIconColor(icon);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 3),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.08),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SwitchListTile(
        secondary: Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: iconColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: iconColor.withValues(alpha: 0.3),
              width: 1.5,
            ),
          ),
          child: Icon(icon, color: iconColor, size: 22),
        ),
        title: Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            fontSize: 13,
            color: Colors.grey[600],
            height: 1.2,
          ),
        ),
        value: value,
        onChanged: onChanged,
        activeColor: const Color(0xFF2A5298),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      ),
    );
  }

  void _showAboutDialog() {
    showAboutDialog(
      context: context,
      applicationName: 'محاسب',
      applicationVersion: _appVersion,
      applicationIcon: Container(
        width: 64,
        height: 64,
        decoration: BoxDecoration(
          color: Colors.blue[600],
          borderRadius: BorderRadius.circular(16),
        ),
        child: const Icon(Icons.calculate, color: Colors.white, size: 32),
      ),
      children: [
        const SizedBox(height: 16),
        const Text(
          'تطبيق محاسب هو حل شامل لإدارة الديون والحسابات المالية. '
          'يساعدك على تتبع المبيعات والمدفوعات بطريقة سهلة ومنظمة.',
          style: TextStyle(fontSize: 14),
        ),
        const SizedBox(height: 16),
        const Text(
          '© 2024 جميع الحقوق محفوظة',
          style: TextStyle(fontSize: 12, color: Colors.grey),
        ),
      ],
    );
  }

  // تم حذف نظام التنبيهات المعقد واستبداله بنظام بسيط

  Widget buildSubNotificationOption({
    required String text,
    required bool value,
    required Future<void> Function(bool) onChanged,
    required Color color,
  }) {
    return Row(
      children: [
        Expanded(
          child: Text(
            text,
            style: const TextStyle(fontSize: 13, color: Color(0xFF34495E)),
          ),
        ),
        Transform.scale(
          scale: 0.8,
          child: Switch(
            value: value,
            onChanged: onChanged,
            activeColor: color,
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
        ),
      ],
    );
  }

  Widget buildFontSection({
    required String title,
    required IconData icon,
    required Color color,
    required Widget child,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withValues(alpha: 0.3), width: 2),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          child,
        ],
      ),
    );
  }

  // دالة تصدير البيانات مع فتح مدير الملفات
  Future<void> _exportDataToFile() async {
    try {
      // عرض رسالة التحميل
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('جاري تصدير البيانات...'),
            backgroundColor: Colors.blue,
            duration: Duration(seconds: 2),
          ),
        );
      }

      // إنشاء النسخة الاحتياطية وقراءة محتواها
      final backupService = BackupService();
      final backupFilePath = await backupService.createFullBackup();

      // قراءة محتوى الملف
      final backupFile = File(backupFilePath);
      final bytes = await backupFile.readAsBytes();

      // إنشاء اسم الملف مع التاريخ والوقت
      final now = DateTime.now();
      final fileName =
          'mahasb_backup_${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}_${now.hour.toString().padLeft(2, '0')}-${now.minute.toString().padLeft(2, '0')}.json';

      // فتح مدير الملفات لاختيار مكان الحفظ
      final result = await FilePicker.platform.saveFile(
        dialogTitle: 'حفظ ملف النسخة الاحتياطية',
        fileName: fileName,
        type: FileType.custom,
        allowedExtensions: ['json'],
        bytes: bytes,
      );

      // تنظيف الملف المؤقت
      try {
        await backupFile.delete();
      } catch (e) {
        debugPrint('تحذير: فشل في حذف الملف المؤقت: $e');
      }

      if (result != null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم حفظ الملف بنجاح في:\n$result'),
              backgroundColor: Colors.green,
              action: SnackBarAction(
                label: 'موافق',
                textColor: Colors.white,
                onPressed: () {},
              ),
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إلغاء عملية الحفظ'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('❌ خطأ في تصدير البيانات: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في تصدير البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // دالة عرض حوار تصدير البيانات
  void _showExportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.cloud_upload, color: Colors.blue[600]),
            const SizedBox(width: 8),
            const Text('تصدير البيانات'),
          ],
        ),
        content: const Text(
          'سيتم تصدير جميع البيانات (العملاء، الديون، المدفوعات) إلى ملف يمكن مشاركته أو حفظه.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _exportDataToFile();
            },
            child: const Text('تصدير'),
          ),
        ],
      ),
    );
  }

  // دالة عرض حوار استيراد البيانات
  void _showImportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.cloud_download, color: Colors.purple[600]),
            const SizedBox(width: 8),
            const Text('استيراد البيانات'),
          ],
        ),
        content: const Text(
          'اختر ملف النسخة الاحتياطية لاستعادة البيانات. سيتم استبدال البيانات الحالية.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _importDataFromFile();
            },
            child: const Text('اختيار ملف'),
          ),
        ],
      ),
    );
  }

  // دالة استيراد البيانات مع فتح مدير الملفات
  Future<void> _importDataFromFile() async {
    try {
      // عرض رسالة التحميل
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('جاري فتح مدير الملفات...'),
            backgroundColor: Colors.purple,
            duration: Duration(seconds: 2),
          ),
        );
      }

      // فتح مدير الملفات لاختيار ملف النسخة الاحتياطية
      final result = await FilePicker.platform.pickFiles(
        dialogTitle: 'اختيار ملف النسخة الاحتياطية',
        type: FileType.custom,
        allowedExtensions: ['json'],
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;

        if (file.path == null) {
          throw Exception('مسار الملف غير صحيح');
        }

        // عرض رسالة الاستيراد
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('جاري استيراد البيانات...'),
              backgroundColor: Colors.blue,
              duration: Duration(seconds: 3),
            ),
          );
        }

        // استيراد البيانات باستخدام خدمة النسخ الاحتياطي
        final backupService = BackupService();
        final success = await backupService.restoreFromBackup(file.path!);

        if (success) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(
                    'تم استيراد البيانات بنجاح! يرجى إعادة تشغيل التطبيق.'),
                backgroundColor: Colors.green,
                duration: Duration(seconds: 5),
              ),
            );
          }
        } else {
          throw Exception('فشل في استيراد البيانات');
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إلغاء اختيار الملف'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('❌ خطأ في استيراد البيانات: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في استيراد البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
